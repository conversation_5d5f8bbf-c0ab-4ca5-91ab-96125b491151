#!/usr/bin/env python3
"""
检查数据库中所有的任务分组
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from config.get_db import get_db


async def check_all_groups():
    """检查数据库中所有的任务分组"""

    async for db in get_db():
        try:
            print("🔍 检查数据库中所有的任务分组...")

            # 1. 查找所有任务分组
            print("\n1. 查找所有任务分组:")
            group_sql = text("SELECT id, group_code, sampling_task_id, status FROM sampling_task_group ORDER BY id DESC")
            group_result = await db.execute(group_sql)
            groups = group_result.fetchall()

            print(f"   找到 {len(groups)} 个任务分组:")
            for group in groups:
                group_id, group_code, task_id, status = group
                print(f"     分组: ID={group_id}, 编号={group_code}, 任务ID={task_id}, 状态={status}")

            # 2. 查找包含25090004的分组
            print(f"\n2. 查找包含25090004的分组:")
            search_sql = text("SELECT id, group_code, sampling_task_id, status FROM sampling_task_group WHERE group_code LIKE '%25090004%' ORDER BY id DESC")
            search_result = await db.execute(search_sql)
            search_groups = search_result.fetchall()

            print(f"   找到 {len(search_groups)} 个包含25090004的分组:")
            for group in search_groups:
                group_id, group_code, task_id, status = group
                print(f"     分组: ID={group_id}, 编号={group_code}, 任务ID={task_id}, 状态={status}")
                
                # 检查每个分组的样品记录
                sample_sql = text("SELECT COUNT(*) FROM sample_record WHERE sampling_task_group_id = :group_id")
                sample_result = await db.execute(sample_sql, {"group_id": group_id})
                sample_count = sample_result.scalar()
                print(f"           样品记录数: {sample_count}")
                
                # 如果有样品记录，检查瓶组关联
                if sample_count > 0:
                    sample_detail_sql = text("SELECT id FROM sample_record WHERE sampling_task_group_id = :group_id LIMIT 1")
                    sample_detail_result = await db.execute(sample_detail_sql, {"group_id": group_id})
                    sample_detail = sample_detail_result.fetchone()
                    
                    if sample_detail:
                        sample_id = sample_detail[0]
                        relation_sql = text("SELECT COUNT(*) FROM sampling_bottle_group_sample WHERE sample_record_id = :sample_id")
                        relation_result = await db.execute(relation_sql, {"sample_id": sample_id})
                        relation_count = relation_result.scalar()
                        print(f"           样品ID={sample_id}, 瓶组关联数: {relation_count}")

            # 3. 查找状态为0（待执行）的分组
            print(f"\n3. 查找状态为0（待执行）的分组:")
            pending_sql = text("SELECT id, group_code, sampling_task_id, status FROM sampling_task_group WHERE status = 0 ORDER BY id DESC LIMIT 10")
            pending_result = await db.execute(pending_sql)
            pending_groups = pending_result.fetchall()

            print(f"   找到 {len(pending_groups)} 个待执行的分组:")
            for group in pending_groups:
                group_id, group_code, task_id, status = group
                print(f"     分组: ID={group_id}, 编号={group_code}, 任务ID={task_id}, 状态={status}")
                
                # 检查样品记录
                sample_sql = text("SELECT COUNT(*) FROM sample_record WHERE sampling_task_group_id = :group_id")
                sample_result = await db.execute(sample_sql, {"group_id": group_id})
                sample_count = sample_result.scalar()
                print(f"           样品记录数: {sample_count}")

            # 4. 创建一个测试分组（如果没有合适的测试数据）
            print(f"\n4. 建议测试方案:")
            if pending_groups:
                test_group = pending_groups[0]
                group_id, group_code, task_id, status = test_group
                print(f"   建议使用分组: {group_code} (ID: {group_id})")
                print(f"   - 该分组状态为待执行，适合测试")
                print(f"   - 可以测试点击开始执行后的瓶组生成流程")
            else:
                print("   没有找到合适的待执行分组进行测试")
                print("   建议创建新的测试数据或检查现有分组的状态")

        except Exception as e:
            print(f"❌ 检查分组时发生错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            break


if __name__ == "__main__":
    asyncio.run(check_all_groups())
