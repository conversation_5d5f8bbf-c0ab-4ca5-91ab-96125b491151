"""
项目报价明细基础价目表DO模型
"""

from datetime import datetime

from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, BigInteger, ForeignKey

from config.database import Base


class ProjectQuotationItemBasedataPrice(Base):
    """
    项目报价明细基础价目表
    """

    __tablename__ = "project_quotation_item_basedata_price"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    project_quotation_id = Column(Integer, ForeignKey("project_quotation.id"), nullable=False, comment="项目报价ID")
    project_quotation_item_id = Column(String(20), nullable=False, comment="项目明细的ID")

    # 以下字段完全来源于 technical_manual_price 表
    method = Column(String(255), nullable=False, comment="检测方法")
    category = Column(String(50), nullable=True, comment="检测类别")
    category_code = Column(String(20), nullable=False, comment="类别编码")
    # classification = Column(String(50), nullable=True, comment='分类')
    first_item_price = Column(DECIMAL(10, 2), nullable=True, comment="检测首项单价")
    additional_item_price = Column(DECIMAL(10, 2), nullable=True, comment="检测增项单价")
    testing_fee_limit = Column(DECIMAL(10, 2), nullable=True, comment="检测费上限")
    sampling_price = Column(DECIMAL(10, 2), nullable=True, comment="采集单价")
    pretreatment_price = Column(DECIMAL(10, 2), nullable=True, comment="前处理单价")
    special_consumables_price = Column(DECIMAL(10, 2), nullable=True, comment="分析特殊耗材单价")

    # 基础字段
    create_by = Column(String(64), nullable=True, comment="创建者")
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment="创建时间")
    remark = Column(String(500), nullable=True, comment="备注")

    def __repr__(self):
        return f"<ProjectQuotationItemBasedataPrice(id={self.id}, category={self.category}, method={self.method})>"

    def price_key(self) -> str:
        return f"{self.category_code}_{self.method}"
