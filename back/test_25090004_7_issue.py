#!/usr/bin/env python3
"""
测试分组编号25090004-7的瓶组生成问题
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_bottle_group_issue_25090004_7():
    """测试分组编号25090004-7的瓶组生成问题"""
    
    print("🔍 测试分组编号25090004-7的瓶组生成问题...")
    
    # 1. 首先查找分组编号25090004-7对应的分组记录
    print("\n1. 查找分组编号25090004-7对应的分组记录:")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/list",
            headers=HEADERS,
            params={"pageNum": 1, "pageSize": 1000},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            groups = data.get('rows', [])
            print(f"   找到 {len(groups)} 个任务分组")
            
            # 查找分组编号25090004-7
            target_group = None
            for group in groups:
                if group.get('groupCode') == '25090004-7':
                    target_group = group
                    break

            # 如果没找到，显示所有包含25090004的分组
            if not target_group:
                print("   可用的25090004分组:")
                matching_groups = [g for g in groups if '25090004' in str(g.get('groupCode', ''))]
                for group in matching_groups[:10]:
                    print(f"     - {group.get('groupCode')} (ID: {group.get('id')}, 状态: {group.get('status')})")

                # 使用第一个待执行的分组进行测试
                pending_groups = [g for g in matching_groups if g.get('status') == 0]
                if pending_groups:
                    target_group = pending_groups[0]
                    print(f"   使用待执行分组进行测试: {target_group.get('groupCode')}")
            
            if target_group:
                print(f"   ✓ 找到目标分组: {target_group.get('groupCode')}")
                print(f"     - 分组ID: {target_group.get('id')}")
                print(f"     - 任务ID: {target_group.get('samplingTaskId')}")
                print(f"     - 状态: {target_group.get('status')}")
                print(f"     - 检测类别: {target_group.get('detectionCategory')}")
                print(f"     - 点位名称: {target_group.get('pointName')}")
                
                group_id = target_group.get('id')
                task_id = target_group.get('samplingTaskId')
                
                # 2. 检查该分组是否已有样品记录
                print(f"\n2. 检查分组 {group_id} 的样品记录:")
                try:
                    response = requests.get(
                        f"{BASE_URL}/sampling/sample-records/group/{group_id}",
                        headers=HEADERS,
                        timeout=10
                    )
                    
                    print(f"   状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        sample_records = data.get('data', [])
                        print(f"   ✓ 找到 {len(sample_records)} 个样品记录")
                        
                        if sample_records:
                            for i, record in enumerate(sample_records[:3], 1):
                                print(f"     样品{i}: ID={record.get('id')}, 编号={record.get('sampleNumber')}")
                                print(f"             检测类别={record.get('detectionCategory')}")
                                print(f"             状态={record.get('status')}")
                            
                            if len(sample_records) > 3:
                                print(f"     ... 还有 {len(sample_records) - 3} 个样品记录")
                                
                            # 3. 检查样品记录关联的瓶组
                            sample_id = sample_records[0].get('id')
                            print(f"\n3. 检查样品ID {sample_id} 关联的瓶组:")
                            try:
                                response = requests.get(
                                    f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
                                    headers=HEADERS,
                                    timeout=10
                                )
                                
                                print(f"   状态码: {response.status_code}")
                                
                                if response.status_code == 200:
                                    data = response.json()
                                    bottle_groups = data.get('data', [])
                                    print(f"   ✓ 样品 {sample_id} 关联了 {len(bottle_groups)} 个瓶组")
                                    
                                    if bottle_groups:
                                        for i, group in enumerate(bottle_groups[:5], 1):
                                            print(f"     瓶组{i}: ID={group.get('id')}, 编号={group.get('bottleGroupCode')}")
                                            print(f"             检测方法={group.get('detectionMethod')[:50]}...")
                                            print(f"             状态={group.get('status')}")
                                        
                                        if len(bottle_groups) > 5:
                                            print(f"     ... 还有 {len(bottle_groups) - 5} 个瓶组")
                                    else:
                                        print("   ❌ 样品没有关联任何瓶组 - 这就是问题所在！")
                                        
                                        # 4. 检查任务的瓶组信息
                                        print(f"\n4. 检查任务 {task_id} 的瓶组信息:")
                                        try:
                                            response = requests.get(
                                                f"{BASE_URL}/sampling/bottle-groups/task/{task_id}",
                                                headers=HEADERS,
                                                timeout=10
                                            )
                                            
                                            print(f"   状态码: {response.status_code}")
                                            
                                            if response.status_code == 200:
                                                data = response.json()
                                                bottle_groups = data.get('data', [])
                                                print(f"   ✓ 任务 {task_id} 有 {len(bottle_groups)} 个瓶组")
                                                
                                                if bottle_groups:
                                                    print("   前5个瓶组:")
                                                    for i, group in enumerate(bottle_groups[:5], 1):
                                                        print(f"     瓶组{i}: ID={group.get('id')}, 编号={group.get('bottleGroupCode')}")
                                                        print(f"             检测方法={group.get('detectionMethod')[:50]}...")
                                                        print(f"             样品数量={group.get('sampleCount')}")
                                                        print(f"             状态={group.get('status')}")
                                                    
                                                    if len(bottle_groups) > 5:
                                                        print(f"     ... 还有 {len(bottle_groups) - 5} 个瓶组")
                                                        
                                                    print(f"\n   📊 问题分析:")
                                                    print(f"   - 任务有 {len(bottle_groups)} 个瓶组")
                                                    print(f"   - 样品记录存在但没有关联到瓶组")
                                                    print(f"   - 这是数据关联完整性问题")
                                                else:
                                                    print("   ❌ 任务没有瓶组，需要生成瓶组")
                                                    
                                                    # 5. 尝试生成瓶组
                                                    print(f"\n5. 为任务 {task_id} 生成瓶组:")
                                                    try:
                                                        response = requests.post(
                                                            f"{BASE_URL}/sampling/bottle-groups/generate/{task_id}",
                                                            headers=HEADERS,
                                                            timeout=30
                                                        )
                                                        
                                                        print(f"   状态码: {response.status_code}")
                                                        
                                                        if response.status_code == 200:
                                                            data = response.json()
                                                            print("   ✓ 瓶组生成成功")
                                                            print(f"   响应消息: {data.get('msg', '')}")
                                                            
                                                            bottle_data = data.get('data', {})
                                                            print(f"   - 总瓶组数: {bottle_data.get('totalGroups', 0)}")
                                                            print(f"   - 默认瓶组数: {bottle_data.get('defaultGroups', 0)}")
                                                            print(f"   - 匹配瓶组数: {bottle_data.get('matchedGroups', 0)}")
                                                        else:
                                                            print(f"   ✗ 瓶组生成失败，状态码: {response.status_code}")
                                                            print(f"   响应内容: {response.text}")
                                                            
                                                    except Exception as e:
                                                        print(f"   ✗ 生成瓶组异常: {e}")
                                            else:
                                                print(f"   ✗ 获取瓶组信息失败，状态码: {response.status_code}")
                                                print(f"   响应内容: {response.text}")
                                                
                                        except Exception as e:
                                            print(f"   ✗ 获取瓶组信息异常: {e}")
                                else:
                                    print(f"   ✗ API调用失败，状态码: {response.status_code}")
                                    print(f"   响应内容: {response.text}")
                                    
                            except Exception as e:
                                print(f"   ✗ API调用异常: {e}")
                        else:
                            print("   ⚠️ 该分组没有样品记录，需要先生成样品记录")
                            
                            # 生成样品记录
                            print(f"\n3. 为分组 {group_id} 生成样品记录:")
                            try:
                                response = requests.post(
                                    f"{BASE_URL}/sampling/sample-records/generate/group/{group_id}",
                                    headers=HEADERS,
                                    timeout=30
                                )
                                
                                print(f"   状态码: {response.status_code}")
                                
                                if response.status_code == 200:
                                    data = response.json()
                                    print("   ✓ 样品记录生成成功")
                                    
                                    sample_records = data.get('data', [])
                                    print(f"   - 生成样品记录数: {len(sample_records)}")
                                    
                                    if sample_records:
                                        sample_id = sample_records[0].get('id')
                                        print(f"   - 第一个样品ID: {sample_id}")
                                        
                                        # 检查生成后的瓶组关联
                                        print(f"\n4. 检查生成后样品ID {sample_id} 关联的瓶组:")
                                        try:
                                            response = requests.get(
                                                f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
                                                headers=HEADERS,
                                                timeout=10
                                            )
                                            
                                            print(f"   状态码: {response.status_code}")
                                            
                                            if response.status_code == 200:
                                                data = response.json()
                                                bottle_groups = data.get('data', [])
                                                print(f"   ✓ 样品 {sample_id} 关联了 {len(bottle_groups)} 个瓶组")
                                                
                                                if bottle_groups:
                                                    print("   前5个瓶组:")
                                                    for i, group in enumerate(bottle_groups[:5], 1):
                                                        print(f"     瓶组{i}: ID={group.get('id')}, 编号={group.get('bottleGroupCode')}")
                                                        print(f"             检测方法={group.get('detectionMethod')[:50]}...")
                                                    
                                                    if len(bottle_groups) > 5:
                                                        print(f"     ... 还有 {len(bottle_groups) - 5} 个瓶组")
                                                        
                                                    print(f"\n   ✅ 问题已解决！样品记录现在正确关联到瓶组")
                                                else:
                                                    print("   ❌ 样品记录生成后仍然没有关联瓶组")
                                                    print("   这表明瓶组生成逻辑存在问题")
                                            else:
                                                print(f"   ✗ API调用失败，状态码: {response.status_code}")
                                                
                                        except Exception as e:
                                            print(f"   ✗ API调用异常: {e}")
                                else:
                                    print(f"   ✗ 样品记录生成失败，状态码: {response.status_code}")
                                    print(f"   响应内容: {response.text}")
                                    
                            except Exception as e:
                                print(f"   ✗ 生成样品记录异常: {e}")
                    else:
                        print(f"   ✗ 获取样品记录失败，状态码: {response.status_code}")
                        print(f"   响应内容: {response.text}")
                        
                except Exception as e:
                    print(f"   ✗ 获取样品记录异常: {e}")
                
            else:
                print("   ✗ 未找到分组编号25090004-7")
                print("   查找包含25090004的分组:")
                matching_groups = [g for g in groups if '25090004' in str(g.get('groupCode', ''))]
                if matching_groups:
                    for group in matching_groups:
                        print(f"     - {group.get('groupCode')} (ID: {group.get('id')}, 任务ID: {group.get('samplingTaskId')}, 状态: {group.get('status')})")
                else:
                    print("     没有找到包含25090004的分组")
        else:
            print(f"   ✗ 获取分组列表失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except Exception as e:
        print(f"   ✗ 获取分组列表异常: {e}")

if __name__ == "__main__":
    test_bottle_group_issue_25090004_7()
