#!/usr/bin/env python3
"""
测试瓶组信息显示问题
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from config.get_db import get_db


async def test_bottle_group_issue():
    """测试瓶组信息显示问题"""

    async for db in get_db():
        try:
            print("🔍 开始检查瓶组信息显示问题...")

            # 1. 查找最新的采样任务
            task_sql = text("SELECT id, task_code, task_name FROM sampling_task ORDER BY id DESC LIMIT 1")
            task_result = await db.execute(task_sql)
            task_row = task_result.fetchone()

            if not task_row:
                print("❌ 没有找到采样任务")
                return

            task_id, task_code, task_name = task_row
            print(f"📋 找到最新任务: ID={task_id}, 编号={task_code}, 名称={task_name}")

            # 2. 查找该任务的分组
            group_sql = text("SELECT id, group_code FROM sampling_task_group WHERE sampling_task_id = :task_id ORDER BY id DESC LIMIT 1")
            group_result = await db.execute(group_sql, {"task_id": task_id})
            group_row = group_result.fetchone()

            if not group_row:
                print("❌ 该任务没有分组")
                return

            group_id, group_code = group_row
            print(f"📂 找到分组: ID={group_id}, 编号={group_code}")

            # 3. 查找该分组的样品记录
            sample_sql = text("SELECT id, sample_number FROM sample_record WHERE sampling_task_group_id = :group_id ORDER BY sample_number")
            sample_result = await db.execute(sample_sql, {"group_id": group_id})
            samples = sample_result.fetchall()
            
            print(f"🧪 该分组有 {len(samples)} 个样品记录")

            if not samples:
                print("❌ 该分组没有样品记录")
                return

            # 4. 检查瓶组是否存在
            bottle_sql = text("SELECT id, bottle_group_code, status FROM sampling_bottle_group WHERE sampling_task_id = :task_id ORDER BY bottle_group_code")
            bottle_result = await db.execute(bottle_sql, {"task_id": task_id})
            bottle_groups = bottle_result.fetchall()

            print(f"🍶 该任务有 {len(bottle_groups)} 个瓶组")

            if bottle_groups:
                for bottle in bottle_groups:
                    bottle_id, bottle_code, status = bottle
                    print(f"  瓶组: {bottle_code} (ID: {bottle_id}, 状态: {status})")
            else:
                print("❌ 该任务没有瓶组")
            
            # 5. 检查样品与瓶组的关联关系
            print("\n🔗 检查样品与瓶组的关联关系:")

            # 查询样品与瓶组的关联表
            relation_sql = text("""
                SELECT
                    sr.id as sample_id,
                    sr.sample_number,
                    bg.id as bottle_id,
                    bg.bottle_group_code,
                    bg.status
                FROM sample_record sr
                LEFT JOIN sampling_bottle_group_sample bgs ON sr.id = bgs.sample_record_id
                LEFT JOIN sampling_bottle_group bg ON bgs.bottle_group_id = bg.id
                WHERE sr.sampling_task_group_id = :group_id
                ORDER BY sr.sample_number, bg.bottle_group_code
            """)

            relation_result = await db.execute(relation_sql, {"group_id": group_id})
            relations = relation_result.fetchall()

            if relations:
                current_sample_id = None
                for relation in relations:
                    sample_id, sample_number, bottle_id, bottle_code, bottle_status = relation

                    if sample_id != current_sample_id:
                        print(f"\n样品 {sample_number} (ID: {sample_id}):")
                        current_sample_id = sample_id

                    if bottle_id:
                        print(f"  ✅ 关联瓶组: {bottle_code} (ID: {bottle_id}, 状态: {bottle_status})")
                    else:
                        print(f"  ❌ 没有关联瓶组")
            else:
                print("❌ 没有找到样品与瓶组的关联关系")
            
            # 6. 检查数据库表结构
            print(f"\n📊 数据库统计:")

            tables_to_check = [
                'sampling_task',
                'sampling_task_group',
                'sample_record',
                'sampling_bottle_group',
                'sampling_bottle_group_sample'
            ]

            for table in tables_to_check:
                try:
                    result = await db.execute(text(f"SELECT COUNT(*) as count FROM {table}"))
                    count = result.scalar()
                    print(f"  {table}: {count} 条记录")
                except Exception as e:
                    print(f"  {table}: 查询失败 - {e}")

            # 7. 分析问题原因
            print(f"\n🔍 问题分析:")

            if len(samples) > 0 and len(bottle_groups) > 0:
                # 检查关联表是否有数据
                relation_count_sql = text("SELECT COUNT(*) FROM sampling_bottle_group_sample")
                relation_count_result = await db.execute(relation_count_sql)
                relation_count = relation_count_result.scalar()

                print(f"  样品记录数: {len(samples)}")
                print(f"  瓶组数: {len(bottle_groups)}")
                print(f"  关联关系数: {relation_count}")

                if relation_count == 0:
                    print("  ⚠️  问题原因: 样品记录与瓶组没有建立关联关系")
                    print("  💡 解决方案: 需要在生成瓶组时建立样品与瓶组的关联关系")
                else:
                    print("  ✅ 关联关系存在，可能是前端API调用问题")

            await db.commit()
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            await db.rollback()
        finally:
            await db.close()


if __name__ == "__main__":
    asyncio.run(test_bottle_group_issue())
