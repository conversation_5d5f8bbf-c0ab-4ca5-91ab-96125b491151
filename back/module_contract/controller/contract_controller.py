"""
合同管理控制器
"""

from typing import List
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_contract.entity.vo.contract_vo import (
    AddContractModel,
    EditContractModel,
    ContractQueryModel,
    ContractResponseModel,
)
from module_contract.service.contract_service import ContractService
from utils.response_util import ResponseUtil
from utils.page_util import PageResponseModel


contractController = APIRouter(prefix="/contract", tags=["合同管理"])


@contractController.get("/list", response_model=PageResponseModel)
async def get_contract_list(
    page_num: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    contract_name: str = Query(None, description="合同名称"),
    contract_number: str = Query(None, description="合同编号"),
    business_type: str = Query(None, description="业务类型"),
    client_name: str = Query(None, description="委托单位"),
    project_manager: str = Query(None, description="项目负责人"),
    contract_status: str = Query(None, description="合同状态"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """获取合同列表"""
    query_model = ContractQueryModel(
        page_num=page_num,
        page_size=page_size,
        contract_name=contract_name,
        contract_number=contract_number,
        business_type=business_type,
        client_name=client_name,
        project_manager=project_manager,
        contract_status=contract_status,
    )

    contract_service = ContractService(query_db)
    result = await contract_service.get_contract_list(query_model)

    return ResponseUtil.success(data=result)


@contractController.get("/{contract_id}", response_model=CrudResponseModel)
async def get_contract_detail(
    contract_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """获取合同详情"""
    contract_service = ContractService(query_db)
    result = await contract_service.get_contract_detail(contract_id)

    return ResponseUtil.success(data=result)


@contractController.post("/add", response_model=CrudResponseModel)
async def add_contract(
    contract_data: AddContractModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """新增合同"""
    contract_service = ContractService(query_db)
    result = await contract_service.add_contract(contract_data, current_user)

    return ResponseUtil.success(data=result, msg=result["message"])


@contractController.put("/update", response_model=CrudResponseModel)
async def update_contract(
    contract_data: EditContractModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """更新合同"""
    contract_service = ContractService(query_db)
    result = await contract_service.update_contract(contract_data, current_user)

    return ResponseUtil.success(data=result, msg=result["message"])


@contractController.delete("/{contract_id}", response_model=CrudResponseModel)
async def delete_contract(
    contract_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """删除合同"""
    contract_service = ContractService(query_db)
    result = await contract_service.delete_contract(contract_id, current_user)

    return ResponseUtil.success(data=result, msg=result["message"])


@contractController.delete("/batch", response_model=CrudResponseModel)
async def batch_delete_contracts(
    contract_ids: List[int],
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """批量删除合同"""
    contract_service = ContractService(query_db)
    result = await contract_service.batch_delete_contracts(contract_ids, current_user)

    return ResponseUtil.success(data=result, msg=result["message"])
