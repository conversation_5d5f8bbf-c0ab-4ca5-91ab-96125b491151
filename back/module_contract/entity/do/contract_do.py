"""
合同管理数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Numeric, JSON
from config.database import Base


class Contract(Base):
    """
    合同管理表
    """

    __tablename__ = "contract"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")

    # 基本信息
    contract_name = Column(String(500), nullable=False, comment="合同名称")
    contract_number = Column(String(100), nullable=True, comment="合同编号")
    external_contract_number = Column(String(100), nullable=True, comment="外部合同编号")
    business_type = Column(String(50), nullable=False, comment="业务类型")
    region_province = Column(String(50), nullable=False, comment="省份")
    region_city = Column(String(50), nullable=False, comment="城市")
    client_name = Column(String(200), nullable=False, comment="委托单位")
    client_contact = Column(String(100), nullable=False, comment="委托单位联系人")
    acquisition_method = Column(String(50), nullable=False, comment="取得方式")
    project_manager_ids = Column(JSON, nullable=False, comment="项目负责人ID数组")
    project_service_id = Column(Integer, nullable=False, comment="项目客服ID")
    dept_id = Column(Integer, nullable=True, comment="所属部门ID")
    contract_sign_date = Column(Date, nullable=True, comment="合同签订日期")
    amount_type = Column(String(20), nullable=False, comment="金额类型")
    contract_amount = Column(Numeric(15, 2), nullable=False, comment="合同金额")
    quotation_total_amount = Column(Numeric(15, 2), nullable=True, comment="报价单总金额")
    changed_contract_amount = Column(Numeric(15, 2), nullable=True, comment="变更后合同金额")
    outsourcing_amount = Column(Numeric(15, 2), nullable=True, comment="外协金额")
    outsourcing_company = Column(String(200), nullable=True, comment="外协单位")
    completion_time = Column(DateTime, nullable=True, comment="完工时间")
    contract_status = Column(String(50), nullable=True, comment="合同状态")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
    del_flag = Column(String(1), nullable=True, default="0", comment="删除标志(0代表存在 1代表删除)")
    remark = Column(Text, nullable=True, comment="备注")
