#!/usr/bin/env python3
"""
直接测试API和数据库的差异
"""

import requests
import json
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from config.get_db import get_db

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

async def compare_api_and_db():
    """比较API和数据库的数据"""
    
    print("🔍 比较API和数据库的数据...")
    
    # 1. 测试API
    print("\n1. 测试API获取分组列表:")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/list",
            headers=HEADERS,
            params={"pageNum": 1, "pageSize": 100},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            api_groups = data.get('rows', [])
            print(f"   API返回 {len(api_groups)} 个分组")
            
            if api_groups:
                print("   前5个分组:")
                for i, group in enumerate(api_groups[:5], 1):
                    print(f"     {i}. {group.get('groupCode')} (ID: {group.get('id')}, 状态: {group.get('status')})")
            else:
                print("   ⚠️ API返回空列表")
        else:
            print(f"   ✗ API调用失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except Exception as e:
        print(f"   ✗ API调用异常: {e}")
    
    # 2. 测试数据库
    print("\n2. 测试数据库查询:")
    async for db in get_db():
        try:
            group_sql = text("SELECT id, group_code, sampling_task_id, status FROM sampling_task_group ORDER BY id DESC LIMIT 10")
            group_result = await db.execute(group_sql)
            db_groups = group_result.fetchall()
            
            print(f"   数据库返回 {len(db_groups)} 个分组")
            
            if db_groups:
                print("   前5个分组:")
                for i, group in enumerate(db_groups[:5], 1):
                    group_id, group_code, task_id, status = group
                    print(f"     {i}. {group_code} (ID: {group_id}, 状态: {status})")
            else:
                print("   ⚠️ 数据库返回空列表")
                
        except Exception as e:
            print(f"   ✗ 数据库查询异常: {e}")
        finally:
            break
    
    # 3. 测试特定分组API
    print("\n3. 测试特定分组API:")
    group_id = 29  # 25090004-7的ID
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/{group_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            group_detail = data.get('data', {})
            print(f"   ✓ 成功获取分组详情")
            print(f"     - 分组编号: {group_detail.get('groupCode')}")
            print(f"     - 任务ID: {group_detail.get('samplingTaskId')}")
            print(f"     - 状态: {group_detail.get('status')}")
            print(f"     - 检测类别: {group_detail.get('detectionCategory')}")
            print(f"     - 点位名称: {group_detail.get('pointName')}")
        else:
            print(f"   ✗ API调用失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except Exception as e:
        print(f"   ✗ API调用异常: {e}")
    
    # 4. 测试样品记录生成API
    print(f"\n4. 测试为分组 {group_id} 生成样品记录:")
    try:
        response = requests.post(
            f"{BASE_URL}/sampling/sample-records/generate/group/{group_id}",
            headers=HEADERS,
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✓ 样品记录生成成功")
            
            sample_records = data.get('data', [])
            print(f"   - 生成样品记录数: {len(sample_records)}")
            
            if sample_records:
                sample_id = sample_records[0].get('id')
                print(f"   - 第一个样品ID: {sample_id}")
                
                # 检查瓶组关联
                print(f"\n5. 检查样品ID {sample_id} 的瓶组关联:")
                try:
                    response = requests.get(
                        f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
                        headers=HEADERS,
                        timeout=10
                    )
                    
                    print(f"   状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        bottle_groups = data.get('data', [])
                        print(f"   ✓ 样品 {sample_id} 关联了 {len(bottle_groups)} 个瓶组")
                        
                        if bottle_groups:
                            print("   前3个瓶组:")
                            for i, group in enumerate(bottle_groups[:3], 1):
                                print(f"     瓶组{i}: ID={group.get('id')}, 编号={group.get('bottleGroupCode')}")
                                print(f"             检测方法={group.get('detectionMethod')[:50]}...")
                            
                            if len(bottle_groups) > 3:
                                print(f"     ... 还有 {len(bottle_groups) - 3} 个瓶组")
                                
                            print(f"\n   ✅ 问题已解决！样品记录现在正确关联到瓶组")
                        else:
                            print("   ❌ 样品记录生成后仍然没有关联瓶组")
                            print("   这表明瓶组生成逻辑存在问题")
                    else:
                        print(f"   ✗ API调用失败，状态码: {response.status_code}")
                        print(f"   响应内容: {response.text}")
                        
                except Exception as e:
                    print(f"   ✗ API调用异常: {e}")
        else:
            print(f"   ✗ 样品记录生成失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except Exception as e:
        print(f"   ✗ 生成样品记录异常: {e}")

if __name__ == "__main__":
    asyncio.run(compare_api_and_db())
