<template>
  <el-tree-select
    v-model="selectedValue"
    :data="deptOptions"
    :props="treeProps"
    :placeholder="placeholder"
    :clearable="clearable"
    :filterable="filterable"
    :check-strictly="checkStrictly"
    :render-after-expand="false"
    :expand-on-click-node="false"
    style="width: 100%"
    @change="handleChange"
  />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { deptTreeSelect } from '@/api/system/user'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  placeholder: {
    type: String,
    default: '请选择部门'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: true
  },
  checkStrictly: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedValue = ref(props.modelValue)
const deptOptions = ref([])

const treeProps = {
  children: 'children',
  label: 'label',
  value: 'id'
}

// 获取部门树数据
const getDeptTree = async () => {
  try {
    const response = await deptTreeSelect()
    deptOptions.value = response.data || []
  } catch (error) {
    console.error('获取部门树失败:', error)
    deptOptions.value = []
  }
}

// 处理选择变化
const handleChange = (value) => {
  selectedValue.value = value
  emit('update:modelValue', value)
  emit('change', value)
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedValue.value = newVal
}, { immediate: true })

onMounted(() => {
  getDeptTree()
})
</script>

<style scoped>
:deep(.el-tree-select) {
  width: 100%;
}
</style>
