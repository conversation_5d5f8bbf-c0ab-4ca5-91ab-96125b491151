<template>
  <div class="contract-business">
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">合同商务信息</span>
    </el-divider>

    <!-- 分摊类型选择 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="8">
        <el-form-item label="分摊类型">
          <el-select v-model="currentType" placeholder="请选择分摊类型" @change="handleTypeChange">
            <el-option label="部门分摊" value="department" />
            <el-option label="采样任务" value="task" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-button type="primary" @click="addBusinessItem" :disabled="!currentType">
          <el-icon><Plus /></el-icon>
          添加{{ currentType === 'department' ? '部门分摊' : '采样任务' }}
        </el-button>
      </el-col>
    </el-row>

    <!-- 部门分摊列表 -->
    <div v-if="departmentList.length > 0" class="business-section">
      <h4>部门分摊</h4>
      <el-table :data="departmentList" border>
        <el-table-column label="项目报价编号" width="200">
          <template #default="scope">
            <el-select
              v-model="scope.row.projectCode"
              placeholder="请选择项目报价编号"
              filterable
              remote
              :remote-method="searchQuotations"
              :loading="quotationLoading"
              @change="handleQuotationChange(scope.row, $event)"
            >
              <el-option
                v-for="item in quotationOptions"
                :key="item.projectCode"
                :label="item.projectCode"
                :value="item.projectCode"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="部门" width="200">
          <template #default="scope">
            <DeptTreeSelect v-model="scope.row.deptId" placeholder="请选择部门" />
          </template>
        </el-table-column>
        <el-table-column label="分摊金额" width="150">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.allocationAmount"
              :precision="2"
              :min="0"
              placeholder="分摊金额"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="danger" size="small" @click="removeDepartmentItem(scope.$index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 采样任务列表 -->
    <div v-if="taskList.length > 0" class="business-section">
      <h4>采样任务</h4>
      <el-table :data="taskList" border>
        <el-table-column label="项目报价编号" width="200">
          <template #default="scope">
            <el-select
              v-model="scope.row.projectCode"
              placeholder="请选择报价编号"
              filterable
              remote
              :remote-method="searchQuotations"
              :loading="quotationLoading"
              @change="handleQuotationChange(scope.row, $event)"
            >
              <el-option
                v-for="item in quotationOptions"
                :key="item.projectCode"
                :label="item.projectCode"
                :value="item.projectCode"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="任务编号" width="200">
          <template #default="scope">
            <el-select
              v-model="scope.row.taskCode"
              placeholder="请选择任务编号"
              filterable
              remote
              :remote-method="searchTasks"
              :loading="taskLoading"
            >
              <el-option
                v-for="item in taskOptions"
                :key="item.taskCode"
                :label="item.taskCode"
                :value="item.taskCode"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="任务金额" width="150">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.taskAmount"
              :precision="2"
              :min="0"
              placeholder="任务金额"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="danger" size="small" @click="removeTaskItem(scope.$index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 金额汇总 -->
    <div v-if="departmentList.length > 0 || taskList.length > 0" class="amount-summary">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic title="部门分摊总额" :value="departmentTotalAmount" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="任务金额总额" :value="taskTotalAmount" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="合计金额" :value="totalAmount" :precision="2" prefix="¥" />
        </el-col>
      </el-row>
      <div v-if="quotationTotalAmount > 0 && totalAmount > quotationTotalAmount" class="amount-warning">
        <el-alert
          title="警告：分摊金额总和超过报价单总金额"
          type="warning"
          :description="`报价单总金额：¥${quotationTotalAmount.toLocaleString()}，当前分摊总额：¥${totalAmount.toLocaleString()}`"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { pageProjectQuotation } from '@/api/quotation/projectQuotation'
import { getAllGroups } from '@/api/sampling/taskGroup'
import DeptTreeSelect from '@/components/DeptTreeSelect/index.vue'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      departments: [],
      tasks: []
    })
  }
})

const emit = defineEmits(['update:modelValue'])

// 当前选择的分摊类型
const currentType = ref('')

// 数据列表
const departmentList = ref([])
const taskList = ref([])

// 选项数据
const quotationOptions = ref([])
const taskOptions = ref([])

// 加载状态
const quotationLoading = ref(false)
const taskLoading = ref(false)

// 报价单总金额（用于验证）
const quotationTotalAmount = ref(0)

// 计算属性
const departmentTotalAmount = computed(() => {
  return departmentList.value.reduce((sum, item) => sum + (item.allocationAmount || 0), 0)
})

const taskTotalAmount = computed(() => {
  return taskList.value.reduce((sum, item) => sum + (item.taskAmount || 0), 0)
})

const totalAmount = computed(() => {
  return departmentTotalAmount.value + taskTotalAmount.value
})

// 监听数据变化，向父组件发送更新
watch([departmentList, taskList], () => {
  emit('update:modelValue', {
    departments: departmentList.value,
    tasks: taskList.value
  })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    departmentList.value = newVal.departments || []
    taskList.value = newVal.tasks || []
  }
}, { immediate: true, deep: true })

// 处理分摊类型变化
const handleTypeChange = (type) => {
  currentType.value = type
}

// 添加商务项目
const addBusinessItem = () => {
  if (currentType.value === 'department') {
    departmentList.value.push({
      quotationNumber: '',
      deptId: null,
      allocationAmount: 0
    })
  } else if (currentType.value === 'task') {
    taskList.value.push({
      quotationNumber: '',
      taskNumber: '',
      taskAmount: 0
    })
  }
}

// 删除部门分摊项
const removeDepartmentItem = (index) => {
  departmentList.value.splice(index, 1)
}

// 删除任务项
const removeTaskItem = (index) => {
  taskList.value.splice(index, 1)
}

// 搜索报价单
const searchQuotations = async (query) => {
  if (query) {
    quotationLoading.value = true
    try {
      const response = await pageProjectQuotation({
        pageNum: 1,
        pageSize: 1000,
        projectCode: query
      })
      quotationOptions.value = response.data?.rows || response.data || []
    } catch (error) {
      console.error('搜索报价单失败:', error)
      quotationOptions.value = []
    } finally {
      quotationLoading.value = false
    }
  } else {
    quotationOptions.value = []
  }
}

// 搜索任务
const searchTasks = async (query) => {
  if (query) {
    taskLoading.value = true
    try {
      const response = await getAllGroups({
        taskCode: query,
        pageSize: 1000
      })
      taskOptions.value = response.data?.rows || response.data || []
    } catch (error) {
      console.error('搜索任务失败:', error)
      taskOptions.value = []
    } finally {
      taskLoading.value = false
    }
  } else {
    taskOptions.value = []
  }
}

// 处理报价单变化
const handleQuotationChange = (row, projectCode) => {
  const selectedQuotation = quotationOptions.value.find(item => item.projectCode === projectCode)
  if (selectedQuotation) {
    quotationTotalAmount.value = selectedQuotation.totalAmount || 0
  }
}
</script>

<style scoped>
.contract-business {
  padding: 20px;
}

.business-section {
  margin-bottom: 20px;
}

.business-section h4 {
  margin-bottom: 10px;
  color: #409EFF;
}

.amount-summary {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.amount-warning {
  margin-top: 15px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
