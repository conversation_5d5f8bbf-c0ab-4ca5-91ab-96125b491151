<template>
  <div class="contract-detail">
    <!-- 一、基本信息 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">一、基本信息</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="合同名称">{{ contractData.contractName || '-' }}</el-descriptions-item>
      <el-descriptions-item label="合同编号">{{ contractData.contractNumber || '-' }}</el-descriptions-item>
      <el-descriptions-item label="外部合同编号">{{ contractData.externalContractNumber || '-' }}</el-descriptions-item>
      <el-descriptions-item label="业务类型">{{ contractData.businessType || '-' }}</el-descriptions-item>
      <el-descriptions-item label="区域">
        {{ (contractData.regionProvince && contractData.regionCity) ? 
           `${contractData.regionProvince} ${contractData.regionCity}` : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="委托单位">{{ contractData.clientName || '-' }}</el-descriptions-item>
      <el-descriptions-item label="委托单位联系人">{{ contractData.clientContact || '-' }}</el-descriptions-item>
      <el-descriptions-item label="取得方式">{{ contractData.acquisitionMethod || '-' }}</el-descriptions-item>
      <el-descriptions-item label="项目负责人">
        <span v-if="contractData.projectManager && contractData.projectManager.length > 0">
          <el-tag
            v-for="(manager, index) in contractData.projectManager"
            :key="index"
            size="small"
            style="margin: 2px"
            type="primary"
          >
            {{ manager }}
          </el-tag>
        </span>
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="项目客服">{{ contractData.projectService || '-' }}</el-descriptions-item>
      <el-descriptions-item label="所属部门">{{ contractData.deptName || '-' }}</el-descriptions-item>
      <el-descriptions-item label="合同签订日期">{{ contractData.contractSignDate || '-' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 二、金额信息 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">二、金额信息</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="金额类型">{{ contractData.amountType || '-' }}</el-descriptions-item>
      <el-descriptions-item label="合同金额">
        {{ contractData.contractAmount ? '¥' + Number(contractData.contractAmount).toLocaleString() : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="报价单总金额">
        {{ contractData.quotationTotalAmount ? '¥' + Number(contractData.quotationTotalAmount).toLocaleString() : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="变更后合同金额">
        {{ contractData.changedContractAmount ? '¥' + Number(contractData.changedContractAmount).toLocaleString() : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="外协金额">
        {{ contractData.outsourcingAmount ? '¥' + Number(contractData.outsourcingAmount).toLocaleString() : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="外协单位">{{ contractData.outsourcingCompany || '-' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 三、其他信息 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">三、其他信息</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="完工时间">{{ formatDateTime(contractData.completionTime) || '-' }}</el-descriptions-item>
      <el-descriptions-item label="合同状态">
        <el-tag
          v-if="contractData.contractStatus"
          :type="getStatusType(contractData.contractStatus)"
        >
          {{ contractData.contractStatus }}
        </el-tag>
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="创建人">{{ contractData.createBy || '-' }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ formatDateTime(contractData.createTime) || '-' }}</el-descriptions-item>
      <el-descriptions-item label="更新人">{{ contractData.updateBy || '-' }}</el-descriptions-item>
      <el-descriptions-item label="更新时间">{{ formatDateTime(contractData.updateTime) || '-' }}</el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">{{ contractData.remark || '-' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 操作按钮 -->
    <div style="text-align: center; margin-top: 20px;">
      <el-button @click="close">关 闭</el-button>
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(['close'])

const props = defineProps({
  contractData: {
    type: Object,
    default: () => ({})
  }
})

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return date.toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case '草稿':
      return 'info'
    case '待签订':
      return 'warning'
    case '执行中':
      return 'primary'
    case '已完成':
      return 'success'
    case '已终止':
      return 'danger'
    default:
      return 'info'
  }
}

// 关闭
const close = () => {
  emit('close')
}
</script>

<style scoped>
.contract-detail {
  padding: 20px;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-tag {
  margin: 2px;
}
</style>
